# OSS-Fuzz Historical Data SDK

The OSS-Fuzz Historical Data SDK provides a comprehensive interface for accessing, storing, and analyzing historical fuzzing data from OSS-Fuzz projects. This SDK enables researchers and developers to track fuzzing progress, analyze trends, and generate detailed reports.

## Features

- **Unified Interface**: Single SDK facade for all historical data operations
- **Multiple Storage Backends**: Support for local file storage and Google Cloud Storage
- **Comprehensive Data Models**: Structured data models for builds, crashes, coverage, and corpus
- **Advanced Analytics**: Built-in analysis for trends, efficiency, and health scoring
- **Flexible Querying**: Time-based filtering and data aggregation
- **Report Generation**: Automated report generation with health scores and recommendations

## Architecture

The SDK follows a modular architecture with the following components:

### Core Components

- **OSSFuzzSDK**: Main facade class providing unified access to all functionality
- **StorageManager**: Manages different storage backends and provides consistent API
- **StorageAdapter**: Abstract interface with concrete implementations for different storage types

### History Managers

- **BuildHistoryManager**: Manages build history, success rates, and build artifacts
- **CrashHistoryManager**: Handles crash data, deduplication, and crash analysis
- **CorpusHistoryManager**: Tracks corpus growth, statistics, and effectiveness
- **CoverageHistoryManager**: Manages coverage data, trends, and reporting

### Data Models

- **BuildHistoryData**: Build results and metadata
- **CrashHistoryData**: Crash information and analysis
- **CorpusHistoryData**: Corpus statistics and growth metrics
- **CoverageHistoryData**: Coverage measurements and trends
- **TimeSeriesData**: Generic time series data container
- **HistoricalSummary**: Aggregated statistics and summaries

## Quick Start

### Installation

The SDK is part of the OSS-Fuzz Python package. Install it using:

```bash
pip install -e .
```

### Basic Usage

```python
from ossfuzz_py import OSSFuzzSDK

# Initialize SDK with local storage
config = {
    'storage_backend': 'local',
    'storage_path': '/path/to/data'
}

sdk = OSSFuzzSDK('libpng', config)

# Store historical data
build_data = {
    'build_id': 'build_001',
    'success': True,
    'duration_seconds': 300
}
sdk.build.store_build_result(build_data)

# Generate comprehensive report
report = sdk.generate_project_report(days=30)
print(f"Health Score: {report['health_score']['overall_score']}")

# Analyze fuzzing efficiency
efficiency = sdk.analyze_fuzzing_efficiency(days=30)
print(f"Efficiency Level: {efficiency['overall_efficiency']['level']}")
```

### Configuration

#### Local Storage

```python
config = {
    'storage_backend': 'local',
    'storage_path': '/path/to/storage'
}
```

#### Google Cloud Storage

```python
config = {
    'storage_backend': 'gcs',
    'gcs_bucket_name': 'your-bucket-name',
    'gcs_project_id': 'your-project-id',  # optional
    'gcs_credentials_path': '/path/to/credentials.json'  # optional
}
```

#### Environment Variables

You can also configure the SDK using environment variables:

```bash
export OSSFUZZ_HISTORY_STORAGE_BACKEND=local
export OSSFUZZ_HISTORY_STORAGE_PATH=/path/to/storage
export GCS_BUCKET_NAME=your-bucket-name
```

## Advanced Usage

### Working with Build History

```python
# Store build results
build_data = {
    'build_id': 'build_123',
    'project_name': 'libpng',
    'success': True,
    'duration_seconds': 300,
    'commit_hash': 'abc123',
    'sanitizer': 'address'
}
sdk.build.store_build_result(build_data)

# Get build statistics
stats = sdk.build.get_build_statistics(start_date='2025-01-01')
print(f"Success rate: {stats['success_rate']:.1f}%")

# Analyze build trends
trends = sdk.build.get_build_trends(days=30)
print(f"Trend: {trends['trend']}")
```

### Managing Crash Data

```python
# Store crash data with automatic deduplication
crash_data = {
    'crash_id': 'crash_001',
    'fuzzer_name': 'libpng_read_fuzzer',
    'crash_type': 'heap-buffer-overflow',
    'severity': 'HIGH'
}
result = sdk.crash.store_crash(crash_data)

# Check for duplicates
is_duplicate = sdk.crash.is_duplicate_crash(crash_data)

# Get crash statistics
stats = sdk.crash.get_crash_statistics()
print(f"Total crashes: {stats['total_crashes']}")
print(f"Unique crashes: {stats['unique_crashes']}")
```

### Coverage Analysis

```python
# Store coverage data
coverage_data = {
    'fuzzer_name': 'libpng_read_fuzzer',
    'line_coverage': 75.5,
    'function_coverage': 80.0,
    'branch_coverage': 70.0
}
sdk.coverage.store_coverage(coverage_data)

# Generate coverage report
report = sdk.coverage.get_coverage_report()
print(f"Max coverage: {report['summary']['max_line_coverage']:.1f}%")

# Analyze coverage trends
trends = sdk.coverage.analyze_coverage_trends(days=30)
print(f"Coverage trend: {trends['trend']}")
```

### Corpus Management

```python
# Store corpus statistics
corpus_data = {
    'fuzzer_name': 'libpng_read_fuzzer',
    'corpus_size': 1000,
    'total_size_bytes': 5000000,
    'new_files_count': 50
}
sdk.corpus.store_corpus_stats(corpus_data)

# Analyze corpus growth
growth = sdk.corpus.get_corpus_growth(days=30)
print(f"Growth rate: {growth['growth_rate']:.1f}%")

# Merge corpus directories
result = sdk.corpus.merge_corpus('/source/corpus', '/target/corpus')
print(f"Merged {result['copied_files']} files")
```

## Data Storage Format

The SDK stores historical data in a structured format:

```
storage_root/
├── history/
│   ├── build/
│   │   └── {project_name}.json
│   ├── crash/
│   │   └── {project_name}.json
│   ├── coverage/
│   │   └── {fuzzer_name}.json
│   └── corpus/
│       └── {fuzzer_name}.json
└── reports/
    ├── project_report.json
    └── efficiency_analysis.json
```

Each JSON file contains an array of historical entries with timestamps, allowing for time-based queries and analysis.

## Error Handling

The SDK provides comprehensive error handling with specific exception types:

```python
from ossfuzz_py.errors import (
    OSSFuzzSDKError,
    OSSFuzzSDKConfigError,
    HistoryManagerError,
    HistoryStorageError,
    HistoryRetrievalError,
    HistoryValidationError
)

try:
    sdk = OSSFuzzSDK('project_name', config)
    report = sdk.generate_project_report()
except OSSFuzzSDKConfigError as e:
    print(f"Configuration error: {e}")
except HistoryManagerError as e:
    print(f"History management error: {e}")
except OSSFuzzSDKError as e:
    print(f"General SDK error: {e}")
```

## Examples

See the `examples/historical_data_example.py` file for a comprehensive example demonstrating all SDK features.

## Testing

Run the test suite to verify the SDK functionality:

```bash
python -m pytest ossfuzz_py/unittests/test_historical_data_sdk.py -v
```

## Contributing

When contributing to the Historical Data SDK:

1. Follow existing code patterns and naming conventions
2. Add comprehensive tests for new functionality
3. Update documentation and examples
4. Ensure backward compatibility
5. Run the full test suite before submitting changes

## License

This SDK is part of the OSS-Fuzz project and is licensed under the Apache License 2.0.
