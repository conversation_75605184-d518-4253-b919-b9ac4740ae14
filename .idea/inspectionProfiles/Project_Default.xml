<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="86">
            <item index="0" class="java.lang.String" itemvalue="httpx" />
            <item index="1" class="java.lang.String" itemvalue="tiktoken" />
            <item index="2" class="java.lang.String" itemvalue="protobuf" />
            <item index="3" class="java.lang.String" itemvalue="shapely" />
            <item index="4" class="java.lang.String" itemvalue="google-cloud-audit-log" />
            <item index="5" class="java.lang.String" itemvalue="googleapis-common-protos" />
            <item index="6" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="7" class="java.lang.String" itemvalue="jiter" />
            <item index="8" class="java.lang.String" itemvalue="deprecated" />
            <item index="9" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="10" class="java.lang.String" itemvalue="h11" />
            <item index="11" class="java.lang.String" itemvalue="astroid" />
            <item index="12" class="java.lang.String" itemvalue="gitdb" />
            <item index="13" class="java.lang.String" itemvalue="pydantic-core" />
            <item index="14" class="java.lang.String" itemvalue="markupsafe" />
            <item index="15" class="java.lang.String" itemvalue="google-cloud-logging" />
            <item index="16" class="java.lang.String" itemvalue="fsspec" />
            <item index="17" class="java.lang.String" itemvalue="pyasn1-modules" />
            <item index="18" class="java.lang.String" itemvalue="filelock" />
            <item index="19" class="java.lang.String" itemvalue="mccabe" />
            <item index="20" class="java.lang.String" itemvalue="certifi" />
            <item index="21" class="java.lang.String" itemvalue="anthropic" />
            <item index="22" class="java.lang.String" itemvalue="anyio" />
            <item index="23" class="java.lang.String" itemvalue="gitpython" />
            <item index="24" class="java.lang.String" itemvalue="pyparsing" />
            <item index="25" class="java.lang.String" itemvalue="google-api-core" />
            <item index="26" class="java.lang.String" itemvalue="pyright" />
            <item index="27" class="java.lang.String" itemvalue="tokenizers" />
            <item index="28" class="java.lang.String" itemvalue="google-crc32c" />
            <item index="29" class="java.lang.String" itemvalue="pydantic" />
            <item index="30" class="java.lang.String" itemvalue="nodeenv" />
            <item index="31" class="java.lang.String" itemvalue="opentelemetry-api" />
            <item index="32" class="java.lang.String" itemvalue="wrapt" />
            <item index="33" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="34" class="java.lang.String" itemvalue="google-cloud-aiplatform" />
            <item index="35" class="java.lang.String" itemvalue="jinja2" />
            <item index="36" class="java.lang.String" itemvalue="pyyaml" />
            <item index="37" class="java.lang.String" itemvalue="openai" />
            <item index="38" class="java.lang.String" itemvalue="regex" />
            <item index="39" class="java.lang.String" itemvalue="platformdirs" />
            <item index="40" class="java.lang.String" itemvalue="cxxfilt" />
            <item index="41" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="42" class="java.lang.String" itemvalue="rust-demangler" />
            <item index="43" class="java.lang.String" itemvalue="google-cloud-core" />
            <item index="44" class="java.lang.String" itemvalue="httpcore" />
            <item index="45" class="java.lang.String" itemvalue="idna" />
            <item index="46" class="java.lang.String" itemvalue="google-cloud-bigquery" />
            <item index="47" class="java.lang.String" itemvalue="distro" />
            <item index="48" class="java.lang.String" itemvalue="rsa" />
            <item index="49" class="java.lang.String" itemvalue="smmap" />
            <item index="50" class="java.lang.String" itemvalue="yapf" />
            <item index="51" class="java.lang.String" itemvalue="google-cloud-appengine-logging" />
            <item index="52" class="java.lang.String" itemvalue="httplib2" />
            <item index="53" class="java.lang.String" itemvalue="google-cloud-storage" />
            <item index="54" class="java.lang.String" itemvalue="numpy" />
            <item index="55" class="java.lang.String" itemvalue="pyasn1" />
            <item index="56" class="java.lang.String" itemvalue="requests" />
            <item index="57" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="58" class="java.lang.String" itemvalue="sniffio" />
            <item index="59" class="java.lang.String" itemvalue="grpc-google-iam-v1" />
            <item index="60" class="java.lang.String" itemvalue="google-resumable-media" />
            <item index="61" class="java.lang.String" itemvalue="tomlkit" />
            <item index="62" class="java.lang.String" itemvalue="zipp" />
            <item index="63" class="java.lang.String" itemvalue="google-auth-httplib2" />
            <item index="64" class="java.lang.String" itemvalue="grpcio-status" />
            <item index="65" class="java.lang.String" itemvalue="tomli" />
            <item index="66" class="java.lang.String" itemvalue="urllib3" />
            <item index="67" class="java.lang.String" itemvalue="uritemplate" />
            <item index="68" class="java.lang.String" itemvalue="annotated-types" />
            <item index="69" class="java.lang.String" itemvalue="google-cloud-resource-manager" />
            <item index="70" class="java.lang.String" itemvalue="six" />
            <item index="71" class="java.lang.String" itemvalue="tzdata" />
            <item index="72" class="java.lang.String" itemvalue="dill" />
            <item index="73" class="java.lang.String" itemvalue="packaging" />
            <item index="74" class="java.lang.String" itemvalue="chardet" />
            <item index="75" class="java.lang.String" itemvalue="pandas" />
            <item index="76" class="java.lang.String" itemvalue="tqdm" />
            <item index="77" class="java.lang.String" itemvalue="pylint" />
            <item index="78" class="java.lang.String" itemvalue="proto-plus" />
            <item index="79" class="java.lang.String" itemvalue="cachetools" />
            <item index="80" class="java.lang.String" itemvalue="google-api-python-client" />
            <item index="81" class="java.lang.String" itemvalue="grpcio" />
            <item index="82" class="java.lang.String" itemvalue="isort" />
            <item index="83" class="java.lang.String" itemvalue="docstring-parser" />
            <item index="84" class="java.lang.String" itemvalue="pytz" />
            <item index="85" class="java.lang.String" itemvalue="google-auth" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>