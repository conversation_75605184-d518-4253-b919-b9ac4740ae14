<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectTasksOptions">
    <TaskOptions isEnabled="true">
      <option name="arguments" value="--parallel --in-place --print-modified $FilePath$" />
      <option name="checkSyntaxErrors" value="true" />
      <option name="description" />
      <option name="exitCodeBehavior" value="ERROR" />
      <option name="fileExtension" value="py" />
      <option name="immediateSync" value="true" />
      <option name="name" value="yapf" />
      <option name="output" value="" />
      <option name="outputFilters">
        <array />
      </option>
      <option name="outputFromStdout" value="false" />
      <option name="program" value="$USER_HOME$/.pyenv/versions/3.11.12/envs/oss-env/bin/yapf" />
      <option name="runOnExternalChanges" value="true" />
      <option name="scopeName" value="Project Files" />
      <option name="trackOnlyRoot" value="false" />
      <option name="workingDir" value="" />
      <envs />
    </TaskOptions>
  </component>
</project>