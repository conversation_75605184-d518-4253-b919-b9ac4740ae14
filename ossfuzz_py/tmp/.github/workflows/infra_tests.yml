name: Infra tests

permissions:
  contents: read

on:
  pull_request:
    paths:
      - 'infra/**'
      - '.github/workflows/**'

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      actions: write
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
      cancel-in-progress: true
    steps:
      - uses: actions/checkout@v4
        with:  # Needed for git diff to work. (get_changed_files)
          fetch-depth: 0
      - run: |
          git symbolic-ref refs/remotes/origin/HEAD refs/remotes/origin/master

      - name: Setup python environment
        uses: actions/setup-python@v5
        with:
          python-version: 3.11

      # For gcloud emulators.
      - name: Setup Java environment
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Install dependencies
        run: |
          sudo env "PATH=$PATH" python -m pip install --upgrade pip
          sudo env "PATH=$PATH" pip install -r infra/ci/requirements.txt
          sudo env "PATH=$PATH" pip install -r infra/build/functions/requirements.txt
          sudo env "PATH=$PATH" pip install -r infra/cifuzz/requirements.txt

      - uses: google-github-actions/setup-gcloud@v2
        with:
          version: '523.0.0'
      - run: |
          sudo env "PATH=$PATH" gcloud components install beta cloud-datastore-emulator

      - name: Run infra tests
        run: sudo env "PATH=$PATH" END_TO_END_TESTS=1 INTEGRATION_TESTS=1 python infra/presubmit.py infra-tests -p
