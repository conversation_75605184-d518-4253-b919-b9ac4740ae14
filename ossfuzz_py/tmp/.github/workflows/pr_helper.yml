name: PR helper
on:
  pull_request_target:
    types: [opened]
    branches:
      - master
    paths:
      - 'projects/**'

jobs:
  build:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      pull-requests: write

    steps:
      - uses: actions/checkout@v4
      - name: Setup python environment
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
          cache: pip
          cache-dependency-path: |
            infra/ci/requirements.txt

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r infra/ci/requirements.txt

      - name: setup go environment
        uses: actions/setup-go@v5
        with:
          go-version: 'stable'
      - run: go install github.com/ossf/criticality_score/cmd/criticality_score@latest

      - name: Check if authors are authorized to modify.
        id: checkAuthor
        env:
          GITHUBTOKEN: ${{secrets.GITHUB_TOKEN}}
          PRAUTHOR: ${{ github.event.pull_request.user.login }}
          PRNUMBER: ${{ github.event.pull_request.number }} 
        run: python infra/pr_helper.py

      - name: Leave comments
        if: env.IS_INTERNAL == 'FALSE'
        uses: actions/github-script@v7
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: process.env.MESSAGE
            })

      - name: Add labels for valid PR
        if: env.IS_READY_FOR_MERGE == 'True'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['Ready to merge']
            })
