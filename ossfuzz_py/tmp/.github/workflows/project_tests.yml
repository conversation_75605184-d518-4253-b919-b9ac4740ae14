name: Project tests

permissions:
  contents: read

on:
  pull_request:
    branches:
    - master

jobs:
  build:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.workflow }}-${{ matrix.sanitizer }}-${{ matrix.engine }}-${{ matrix.architecture }}-${{ github.ref }}
      cancel-in-progress: true
    permissions:
      actions: write
    strategy:
      fail-fast: false
      matrix:
        engine:
          - libfuzzer
        sanitizer:
          - address
          - memory
          - undefined
          - coverage
        architecture:
          - x86_64
        include:
          - engine: afl
            sanitizer: address
            architecture: x86_64
          - engine: honggfuzz
            sanitizer: address
            architecture: x86_64
          - engine: libfuzzer
            sanitizer: address
            architecture: i386
          - engine: none
            sanitizer: address
            architecture: x86_64
          - engine: wycheproof
            sanitizer: none
            architecture: x86_64
          - engine: centipede
            sanitizer: address
            architecture: x86_64
          - engine: centipede
            sanitizer: none
            architecture: x86_64
    env:
      ENGINE: ${{ matrix.engine }}
      SANITIZER: ${{ matrix.sanitizer }}
      ARCHITECTURE: ${{ matrix.architecture }}

    steps:
      - uses: actions/checkout@v4
        with:  # Needed for git diff to work. (get_changed_files)
          fetch-depth: 0
      - run: |
          git symbolic-ref refs/remotes/origin/HEAD refs/remotes/origin/master

      - name: Clear unnecessary files
        run: |
          df -h
          sudo swapoff -a
          sudo rm -f /swapfile
          sudo apt clean
          docker rmi $(docker images -a -q) || true
          df -h
          echo "Remove large unused folders, inspired by https://github.com/apache/flink/blame/master/tools/azure-pipelines/free_disk_space.sh"
          sudo bash -c '(ionice -c 3 nice -n 19 rm -rf /usr/share/dotnet/ /usr/local/graalvm/ /usr/local/.ghcup/ /usr/local/share/powershell /usr/local/share/chromium /usr/local/lib/android /usr/local/lib/node_modules)&'

      - name: Setup python environment
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
          cache: pip
          cache-dependency-path: |
            infra/ci/requirements.txt

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r infra/ci/requirements.txt

      - name: Run project tests
        run: python infra/ci/build.py
