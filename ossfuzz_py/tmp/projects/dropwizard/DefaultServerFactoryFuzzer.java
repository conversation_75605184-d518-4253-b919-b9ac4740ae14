// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
///////////////////////////////////////////////////////////////////////////
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.health.HealthCheckRegistry;
import com.code_intelligence.jazzer.api.FuzzedDataProvider;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonFactoryBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.dropwizard.core.Configuration;
import io.dropwizard.core.server.DefaultServerFactory;
import io.dropwizard.core.setup.Environment;

// Generated with https://github.com/ossf/fuzz-introspector/tree/main/tools/auto-fuzz
// Minor modifications to beautify code and ensure exception is caught.
// jvm-autofuzz-heuristics-6
// Heuristic name: jvm-autofuzz-heuristics-6
// Target method: [io.dropwizard.core.server.DefaultServerFactory] public void
// configure(io.dropwizard.core.setup.Environment)
public class DefaultServerFactoryFuzzer {
  public static void fuzzerTestOneInput(FuzzedDataProvider data) {
    DefaultServerFactory obj = new DefaultServerFactory();
    obj.setAdminMaxThreads(data.consumeInt());
    obj.setAdminMinThreads(data.consumeInt());
    obj.setApplicationContextPath(data.consumeString(data.remainingBytes() / 2));
    obj.setAdminContextPath(data.consumeString(data.remainingBytes() / 2));
    obj.configure(new Environment(data.consumeRemainingAsString()));
  }
}
