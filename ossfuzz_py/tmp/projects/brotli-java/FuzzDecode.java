// Copyright 2021 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////////

import com.code_intelligence.jazzer.api.FuzzedDataProvider;

import java.io.*;
import org.brotli.dec.BrotliInputStream;

public class FuzzDecode {
  public static void fuzzerTestOneInput(FuzzedDataProvider data) {
    byte[] buffer = new byte[65536];
    byte[] inputBytes = data.consumeBytes(65536);
    // <PERSON><PERSON><PERSON> allows 0-bit prefix codes - thus even small input could produce large output.
    long totalOutputCap = Math.min(4096L * inputBytes.length, 3L << 24);
    totalOutputCap = Math.max(totalOutputCap, 1L << 20);
    long totalOutput = 0;
    ByteArrayInputStream input = new ByteArrayInputStream(inputBytes);
    try {
      BrotliInputStream brotliInput = new BrotliInputStream(input);
      while (true) {
        int len = brotliInput.read(buffer, 0, buffer.length);
        if (len <= 0) {
          break;
        }
        totalOutput += len;
        if (totalOutput >= totalOutputCap) break;
      }
    } catch (IOException expected) {}
  }
}
