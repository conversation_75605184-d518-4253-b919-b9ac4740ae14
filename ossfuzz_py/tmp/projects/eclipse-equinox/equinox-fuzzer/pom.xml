<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>ossfuzz</groupId>
	<artifactId>equinox-fuzzer</artifactId>
	<version>${fuzzedLibaryVersion}</version>
	<packaging>jar</packaging>
	
	<properties>
		<maven.compiler.source>15</maven.compiler.source>
		<maven.compiler.target>15</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<fuzzedLibaryVersion>3.18.200</fuzzedLibaryVersion>
	</properties>

	<!-- This repositories list is copy and pasted from the projects' main BOM -->
	
	<dependencies>
		<!--
			On the CI, install the jazzer file with
		
				mvn install:install-file -Dfile=${JAZZER_API_PATH} \
					-DgroupId="com.code-intelligence" \
					-DartifactId="jazzer-api" \
					-Dversion="0.14.0" \
					-Dpackaging=jar
		
			in order to avoid mismatching driver/api versions.
		-->
		<dependency>
			<groupId>com.code-intelligence</groupId>
			<artifactId>jazzer-api</artifactId>
			<version>0.14.0</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.osgi</groupId>
			<artifactId>org.eclipse.osgi</artifactId>
			<version>${fuzzedLibaryVersion}</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.platform</groupId>
			<artifactId>org.eclipse.core.runtime</artifactId>
			<version>3.26.100</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>3.3.0</version>
				<configuration>
					<filters>
						<filter>
							<artifact>*:*</artifact>
							<excludes>
								<exclude>META-INF/*.SF</exclude>
								<exclude>META-INF/*.DSA</exclude>
								<exclude>META-INF/*.RSA</exclude>
							</excludes>
						</filter>
					</filters>
				</configuration>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>