# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

FROM gcr.io/oss-fuzz-base/base-builder:v1

RUN git clone --depth 1 --single-branch --branch master https://github.com/boostorg/boost.git
WORKDIR boost
RUN git submodule update --init libs/beast
RUN git -C libs/beast checkout develop
RUN git submodule update --init tools/boostdep
RUN python3 tools/boostdep/depinst/depinst.py --git_args "--jobs 3" beast
COPY build.sh $SRC/
