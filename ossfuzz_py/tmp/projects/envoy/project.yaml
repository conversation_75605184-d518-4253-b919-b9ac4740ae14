homepage: "https://www.envoyproxy.io/"
language: c++
primary_contact: "<EMAIL>"
auto_ccs:
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
coverage_extra_args: -ignore-filename-regex=.*\.cache.*envoy_deps_cache.*
main_repo: 'https://github.com/envoyproxy/envoy.git'
sanitizers:
 - address
 - undefined
fuzzing_engines:
  - libfuzzer
  - honggfuzz
labels:
  "*":
    - ossfuzz-bugz-1149782

