# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""
Integration tests for ResultManager with Builder/Runner pipeline.

This module tests the end-to-end integration of ResultManager with the
Builder and Runner components to ensure proper data flow.
"""

import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

from ossfuzz_py.build.build_config import BuildConfig
from ossfuzz_py.build.builder import LocalBuilder
from ossfuzz_py.result.result_manager import Benchmark
from ossfuzz_py.data.storage_adapter import FileStorageAdapter
from ossfuzz_py.data.storage_manager import StorageManager
from ossfuzz_py.execution.fuzz_runner import LocalRunner
from ossfuzz_py.execution.fuzz_target import FuzzTarget
from ossfuzz_py.history.build_history_manager import BuildHistoryManager
from ossfuzz_py.history.corpus_history_manager import CorpusHistoryManager
from ossfuzz_py.history.coverage_history_manager import CoverageHistoryManager
from ossfuzz_py.history.crash_history_manager import CrashHistoryManager
from ossfuzz_py.result.result_manager import ResultManager
from ossfuzz_py.result.results import BuildInfo, Result


class TestResultManagerIntegration(unittest.TestCase):
  """Test ResultManager integration with Builder/Runner pipeline."""

  def setUp(self):
    """Set up test environment with mocked components."""
    # Create temporary directory for file storage
    self.temp_dir = tempfile.mkdtemp()
    self.storage_path = Path(self.temp_dir)

    # Create storage components
    self.storage_adapter = FileStorageAdapter(str(self.storage_path))
    self.storage_manager = StorageManager({
        'storage_backend': 'local',
        'storage_path': str(self.storage_path)
    })

    # Create HistoryManager instances
    self.project_name = 'test_project'
    self.build_mgr = BuildHistoryManager(self.storage_manager,
                                         self.project_name)
    self.crash_mgr = CrashHistoryManager(self.storage_manager,
                                         self.project_name)
    self.corpus_mgr = CorpusHistoryManager(self.storage_manager,
                                           self.project_name)
    self.coverage_mgr = CoverageHistoryManager(self.storage_manager,
                                               self.project_name)

    # Create ResultManager
    self.result_manager = ResultManager(
        build_mgr=self.build_mgr,
        crash_mgr=self.crash_mgr,
        corpus_mgr=self.corpus_mgr,
        coverage_mgr=self.coverage_mgr,
    )

  def tearDown(self):
    """Clean up test environment."""
    import shutil
    shutil.rmtree(self.temp_dir, ignore_errors=True)

  @patch('ossfuzz_py.build.builder.LocalBuilder.build_local')
  def test_builder_integration(self, mock_build_local):
    """Test integration with LocalBuilder."""
    # Mock successful build
    mock_build_local.return_value = (True, {
        'build_succeeded': True,
        'generated_project': 'test_project_123',
        'target_name': 'test_target',
        'project_name': 'test_project',
    })

    # Create test components
    build_config = BuildConfig(
        project_name='test_project',
        language='c++',
        sanitizer='address',
    )

    # Mock the storage manager for LocalBuilder
    with patch('ossfuzz_py.build.builder.StorageManager') as mock_storage_mgr:
      mock_storage_mgr.return_value = self.storage_manager

      builder = LocalBuilder(build_config)

      # Create test fuzz target
      fuzz_target = FuzzTarget(
          name='test_target',
          source_code='// Test fuzz target source',
          build_script='// Test build script',
          project_name='test_project',
          language='c++',
      )

      # Build the target
      build_result = builder.build(fuzz_target)

      # Verify build succeeded
      self.assertTrue(build_result.success)

      # Create Result object from build
      benchmark = Benchmark(
          project='test_project',
          language='c++',
          function_signature='int test_function(const char* input)',
          function_name='test_function',
          return_type='int',
          target_path='/path/to/test.h',
          id='test_benchmark_builder',
      )

      build_info = BuildInfo(
          compiles=True,
          compile_log='Build successful',
          binary_exists=True,
          is_function_referenced=True,
          fuzz_target_source=fuzz_target.source_code,
          build_script_source=fuzz_target.build_script,
      )

      result = Result(
          benchmark=benchmark,
          work_dirs='/tmp/work',
          trial=1,
          build_info=build_info,
      )

      # Store result through ResultManager
      benchmark_id = 'test_benchmark_builder'
      self.result_manager.store_result(benchmark_id, result)

      # Verify result can be retrieved
      retrieved_result = self.result_manager.get_result(benchmark_id)
      self.assertIsNotNone(retrieved_result)
      self.assertTrue(retrieved_result.is_build_successful())

  @patch('ossfuzz_py.execution.fuzz_runner.LocalRunner._run_target_local')
  def test_runner_integration(self, mock_run_target):
    """Test integration with LocalRunner."""
    # Mock successful run
    mock_run_target.return_value = True

    # Create test components
    with patch(
        'ossfuzz_py.execution.fuzz_runner.StorageManager') as mock_storage_mgr:
      mock_storage_mgr.return_value = self.storage_manager

      runner = LocalRunner()

      # Mock build metadata
      build_metadata = {
          'build_succeeded': True,
          'generated_project': 'test_project_123',
          'target_name': 'test_target',
          'project_name': 'test_project',
      }

      # Create mock run options
      from ossfuzz_py.execution.fuzz_runner import FuzzRunOptions
      run_options = FuzzRunOptions(
          duration_seconds=10,
          extract_coverage=True,
      )

      # Run the target
      run_info = runner.run('test_target', run_options, build_metadata)

      # Verify run completed
      self.assertIsNotNone(run_info)

      # Create Result object from run
      benchmark = Benchmark(
          project='test_project',
          language='c++',
          function_signature='int test_function(const char* input)',
          function_name='test_function',
          return_type='int',
          target_path='/path/to/test.h',
          id='test_benchmark_runner',
      )

      result = Result(
          benchmark=benchmark,
          work_dirs='/tmp/work',
          trial=1,
          run_info=run_info,
      )

      # Store result through ResultManager
      benchmark_id = 'test_benchmark_runner'
      self.result_manager.store_result(benchmark_id, result)

      # Verify result can be retrieved
      retrieved_result = self.result_manager.get_result(benchmark_id)
      self.assertIsNotNone(retrieved_result)

  def test_end_to_end_data_flow(self):
    """Test complete end-to-end data flow without external dependencies."""
    # Create test benchmark
    benchmark = Benchmark(
        project='test_project',
        language='c++',
        function_signature='int test_function(const char* input)',
        function_name='test_function',
        return_type='int',
        target_path='/path/to/test.h',
        id='test_benchmark_e2e',
    )

    # Simulate build stage
    build_info = BuildInfo(
        compiles=True,
        compile_log='Build completed successfully',
        binary_exists=True,
        is_function_referenced=True,
        fuzz_target_source='// Generated fuzz target',
        build_script_source='// Generated build script',
    )

    # Store build result
    build_result = Result(
        benchmark=benchmark,
        work_dirs='/tmp/work',
        trial=1,
        build_info=build_info,
    )

    benchmark_id = 'test_benchmark_e2e'
    self.result_manager.store_result(benchmark_id, build_result)

    # Verify build data was stored
    retrieved_build = self.result_manager.get_result(benchmark_id)
    self.assertIsNotNone(retrieved_build)
    self.assertTrue(retrieved_build.is_build_successful())

    # Simulate run stage (update existing result)
    from ossfuzz_py.result.results import RunInfo
    run_info = RunInfo(
        crashes=False,
        run_log='Fuzzer completed successfully',
        corpus_path='/tmp/corpus',
        cov_pcs=150,
        total_pcs=1000,
    )

    # Store run result
    run_result = Result(
        benchmark=benchmark,
        work_dirs='/tmp/work',
        trial=1,
        build_info=build_info,
        run_info=run_info,
    )

    self.result_manager.store_result(benchmark_id, run_result)

    # Verify complete result
    final_result = self.result_manager.get_result(benchmark_id)
    self.assertIsNotNone(final_result)
    self.assertTrue(final_result.is_build_successful())
    self.assertTrue(final_result.is_run_successful())

    # Test metrics calculation
    metrics = self.result_manager.get_metrics(benchmark_id)
    self.assertIsInstance(metrics, dict)
    self.assertTrue(metrics['compiles'])
    self.assertFalse(metrics['crashes'])
    self.assertEqual(metrics['cov_pcs'], 150)
    self.assertEqual(metrics['total_pcs'], 1000)

  def test_analytics_integration(self):
    """Test analytics and helper methods integration."""
    benchmark_id = 'test_benchmark_analytics'

    # Create and store multiple results to test analytics
    benchmark = Benchmark(
        project='test_project',
        language='c++',
        function_signature='int test_function(const char* input)',
        function_name='test_function',
        return_type='int',
        target_path='/path/to/test.h',
        id=benchmark_id,
    )

    # Store successful build
    build_info = BuildInfo(compiles=True,
                           binary_exists=True,
                           is_function_referenced=True)
    result = Result(benchmark=benchmark,
                    work_dirs='/tmp/work',
                    trial=1,
                    build_info=build_info)
    self.result_manager.store_result(benchmark_id, result)

    # Test build success rate
    success_rate = self.result_manager.get_build_success_rate(benchmark_id)
    self.assertIsInstance(success_rate, float)
    self.assertGreaterEqual(success_rate, 0.0)
    self.assertLessEqual(success_rate, 1.0)

    # Test crash summary
    crash_summary = self.result_manager.get_crash_summary(benchmark_id)
    self.assertIsInstance(crash_summary, dict)
    self.assertIn('total_crashes', crash_summary)

    # Test latest successful build
    latest_build = self.result_manager.latest_successful_build(benchmark_id)
    self.assertIsNotNone(latest_build)
    self.assertTrue(latest_build.is_build_successful())


if __name__ == '__main__':
  unittest.main()
