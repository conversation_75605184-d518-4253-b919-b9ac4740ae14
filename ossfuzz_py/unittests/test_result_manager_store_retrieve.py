# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""
Unit tests for ResultManager store and retrieve operations.

This module tests the core functionality of ResultManager including storing
and retrieving Result objects through HistoryManager integration.
"""

import tempfile
import unittest
from datetime import datetime
from pathlib import Path

from ossfuzz_py.result.result_manager import Benchmark
from ossfuzz_py.data.storage_adapter import FileStorageAdapter
from ossfuzz_py.data.storage_manager import StorageManager
from ossfuzz_py.history.build_history_manager import BuildHistoryManager
from ossfuzz_py.history.corpus_history_manager import CorpusHistoryManager
from ossfuzz_py.history.coverage_history_manager import CoverageHistoryManager
from ossfuzz_py.history.crash_history_manager import CrashHistoryManager
from ossfuzz_py.result.result_manager import ResultManager
from ossfuzz_py.result.results import (AnalysisInfo, BuildInfo,
                                       CoverageAnalysis, CrashAnalysis, Result,
                                       RunInfo)


class TestResultManagerStoreRetrieve(unittest.TestCase):
  """Test ResultManager store and retrieve operations."""

  def setUp(self):
    """Set up test environment with in-memory storage."""
    # Create temporary directory for file storage
    self.temp_dir = tempfile.mkdtemp()
    self.storage_path = Path(self.temp_dir)

    # Create storage components
    self.storage_adapter = FileStorageAdapter(str(self.storage_path))
    self.storage_manager = StorageManager({
        'storage_backend': 'local',
        'storage_path': str(self.storage_path)
    })

    # Create HistoryManager instances
    self.project_name = 'test_project'
    self.build_mgr = BuildHistoryManager(self.storage_manager,
                                         self.project_name)
    self.crash_mgr = CrashHistoryManager(self.storage_manager,
                                         self.project_name)
    self.corpus_mgr = CorpusHistoryManager(self.storage_manager,
                                           self.project_name)
    self.coverage_mgr = CoverageHistoryManager(self.storage_manager,
                                               self.project_name)

    # Create ResultManager
    self.result_manager = ResultManager(
        build_mgr=self.build_mgr,
        crash_mgr=self.crash_mgr,
        corpus_mgr=self.corpus_mgr,
        coverage_mgr=self.coverage_mgr,
    )

  def tearDown(self):
    """Clean up test environment."""
    import shutil
    shutil.rmtree(self.temp_dir, ignore_errors=True)

  def test_store_and_retrieve_complete_result(self):
    """Test storing and retrieving a complete Result with all components."""
    # Create test benchmark
    benchmark = Benchmark(
        project='test_project',
        language='c++',
        function_signature='int test_function(const char* input)',
        function_name='test_function',
        return_type='int',
        target_path='/path/to/test.h',
        id='test_benchmark_1',
    )

    # Create complete Result with all components
    build_info = BuildInfo(
        compiles=True,
        compile_log='Build successful',
        errors=[],
        binary_exists=True,
        is_function_referenced=True,
        fuzz_target_source='// Test fuzz target source',
        build_script_source='// Test build script',
    )

    run_info = RunInfo(
        crashes=True,
        run_log='Fuzzer run log',
        corpus_path='/tmp/corpus',
        reproducer_path='/tmp/reproducer',
        timeout=False,
        error_message='',
        cov_pcs=100,
        total_pcs=1000,
        crash_info='Test crash info',
        log_path='/tmp/log',
        coverage_report_path='/tmp/coverage',
    )

    coverage_analysis = CoverageAnalysis(
        line_coverage=75.5,
        line_coverage_diff=15.2,
        coverage_report_path='/tmp/coverage_report',
        textcov_diff=None,
        cov_pcs=100,
        total_pcs=1000,
    )

    crash_analysis = CrashAnalysis(
        true_bug=True,
        insight='This is a real bug',
        run_error='',
        crash_func={
            'name': 'test_function',
            'line': 42
        },
        crash_symptom='Segmentation fault',
        crash_stacks=[['frame1', 'frame2', 'frame3']],
        crash_info='Detailed crash information',
        has_err=True,
        error_type=None,
    )

    analysis_info = AnalysisInfo(
        crash_analysis=crash_analysis,
        coverage_analysis=coverage_analysis,
    )

    result = Result(
        benchmark=benchmark,
        work_dirs='/tmp/work',
        trial=1,
        iteration=0,
        build_info=build_info,
        run_info=run_info,
        analysis_info=analysis_info,
    )

    # Store the result
    benchmark_id = 'test_benchmark_1'
    self.result_manager.store_result(benchmark_id, result)

    # Retrieve the result
    retrieved_result = self.result_manager.get_result(benchmark_id)

    # Verify result was retrieved
    self.assertIsNotNone(retrieved_result)
    self.assertEqual(retrieved_result.trial, 1)
    self.assertEqual(retrieved_result.benchmark.id, benchmark_id)

    # Verify BuildInfo was preserved
    self.assertIsNotNone(retrieved_result.build_info)
    self.assertTrue(retrieved_result.build_info.compiles)
    self.assertTrue(retrieved_result.build_info.success)

    # Verify RunInfo was preserved (reconstructed from crash data)
    self.assertIsNotNone(retrieved_result.run_info)
    self.assertTrue(retrieved_result.run_info.crashes)

    # Verify AnalysisInfo was preserved (reconstructed from coverage data)
    self.assertIsNotNone(retrieved_result.analysis_info)
    self.assertIsNotNone(retrieved_result.analysis_info.coverage_analysis)
    self.assertEqual(
        retrieved_result.analysis_info.coverage_analysis.line_coverage, 75.5)

  def test_store_multiple_trials(self):
    """Test storing multiple trial results for the same benchmark."""
    benchmark = Benchmark(
        project='test_project',
        language='c++',
        function_signature='int test_function(const char* input)',
        function_name='test_function',
        return_type='int',
        target_path='/path/to/test.h',
        id='test_benchmark_multi',
    )

    benchmark_id = 'test_benchmark_multi'

    # Store 3 trial results
    for trial in range(1, 4):
      build_info = BuildInfo(
          compiles=True,
          compile_log=f'Build successful for trial {trial}',
          binary_exists=True,
          is_function_referenced=True,
      )

      result = Result(
          benchmark=benchmark,
          work_dirs=f'/tmp/work_{trial}',
          trial=trial,
          build_info=build_info,
      )

      self.result_manager.store_result(benchmark_id, result)

    # Retrieve latest result (should be trial 3)
    latest_result = self.result_manager.get_result(benchmark_id)
    self.assertIsNotNone(latest_result)

    # Verify we can retrieve specific trials
    trial_2_result = self.result_manager.get_trial_result(benchmark_id, 2)
    self.assertIsNotNone(trial_2_result)

  def test_retrieve_nonexistent_benchmark(self):
    """Test retrieving result for non-existent benchmark returns None."""
    result = self.result_manager.get_result('nonexistent_benchmark')
    self.assertIsNone(result)

  def test_get_metrics_for_stored_result(self):
    """Test getting metrics for a stored result."""
    # Create and store a result
    benchmark = Benchmark(
        project='test_project',
        language='c++',
        function_signature='int test_function(const char* input)',
        function_name='test_function',
        return_type='int',
        target_path='/path/to/test.h',
        id='test_benchmark_metrics',
    )

    build_info = BuildInfo(compiles=True,
                           binary_exists=True,
                           is_function_referenced=True)
    result = Result(benchmark=benchmark,
                    work_dirs='/tmp/work',
                    trial=1,
                    build_info=build_info)

    benchmark_id = 'test_benchmark_metrics'
    self.result_manager.store_result(benchmark_id, result)

    # Get metrics
    metrics = self.result_manager.get_metrics(benchmark_id)

    # Verify metrics structure
    self.assertIsInstance(metrics, dict)
    self.assertIn('compiles', metrics)
    self.assertIn('crashes', metrics)
    self.assertIn('coverage', metrics)
    self.assertIn('line_coverage_diff', metrics)
    self.assertIn('build_success_rate', metrics)
    self.assertIn('trial', metrics)
    self.assertIn('benchmark_id', metrics)

    # Verify metric values
    self.assertTrue(metrics['compiles'])
    self.assertEqual(metrics['trial'], 1)
    self.assertEqual(metrics['benchmark_id'], benchmark_id)

  def test_error_handling_invalid_result(self):
    """Test error handling for invalid result data."""
    # This test verifies that the ResultManager handles errors gracefully
    # when given invalid or incomplete data

    # Create result with missing required fields
    benchmark = Benchmark(
        project='test_project',
        language='c++',
        function_signature='int test_function(const char* input)',
        function_name='test_function',
        return_type='int',
        target_path='/path/to/test.h',
        id='test_benchmark_error',
    )

    result = Result(
        benchmark=benchmark,
        work_dirs='/tmp/work',
        trial=1,
        # No build_info, run_info, or analysis_info
    )

    # Store should succeed even with minimal data
    benchmark_id = 'test_benchmark_error'
    try:
      self.result_manager.store_result(benchmark_id, result)
    except Exception as e:
      self.fail(f"store_result should handle minimal data gracefully: {e}")

    # Retrieve should return None or minimal result
    retrieved_result = self.result_manager.get_result(benchmark_id)
    # Since we didn't store any actual data components, this might return None
    # which is acceptable behavior


if __name__ == '__main__':
  unittest.main()
